{"name": "My Server", "host": "127.0.0.1", "protocol": "sftp", "port": 6000, "username": "rukbat", "privateKeyPath": "~/.ssh/id_rsa", "remotePath": "/matrix/0-Work/4_exp/dcc7", "uploadOnSave": true, "downloadOnOpen": true, "syncMode": "update", "watcher": {"files": "**/*", "autoUpload": true, "autoDelete": true}, "ignore": ["**/.vscode/**", "**/.git/**", "**/node_modules/**", "**/__pycache__/**", "**/*.pyc", "**/.DS_Store", "**/Thumbs.db", "**/*.tmp", "**/*.log", "**/ui-demo/**", "data"], "syncOption": {"delete": true}}