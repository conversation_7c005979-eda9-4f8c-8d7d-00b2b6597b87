# 商户销售数据时间窗口特征统计分析比赛

## 比赛目标
**以最小的耗时完成商户销售日志的时间窗口特征统计计算**

## 程序要求
- **输入**: 商户销售记录日志文件路径
- **输出**: 统计结果保存为 `result.csv`
- **评判标准**: 程序执行时间（性能优化竞赛）

## 题目理解

### 输入数据格式
- **mid**: 商户ID
- **trading_time**: 交易时间戳（精确到秒）
- **amount**: 销售金额
- **seqno**: 序列号，唯一标识每条记录的时间顺序

### 输出数据格式
对应每个seqno，计算该商户在三个时间窗口内的统计指标：

#### 时间窗口
- **15分钟窗口** (15m)
- **2小时窗口** (2h) 
- **6小时窗口** (6h)

#### 统计指标（每个窗口）
- **sum**: 销售总额
- **frequency**: 销售频次（交易次数）
- **avg**: 平均销售金额
- **stddev**: 标准差
- **zscore**: Z分数
- **max**: 最大销售金额
- **min**: 最小销售金额

#### 格式示例

```csv
seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h
0,528,1,528,0,0,528,528,528,1,528,0,0,528,528,528,1,528,0,0,528,528
```

### 核心要求

1. **一一对应关系**: result.csv的每一行与samples.csv的每一行严格对应
2. **时间窗口计算**: 对于samples中的每条记录，计算该商户在当前时间点（包含）向前回溯的指定时间窗口内的统计数据
3. **商户维度**: 统计计算仅针对同一商户(mid)的历史交易数据
4. **指标计算精准**: 保证指标计算的准确度，最终将比对6位小数的精度

### 开发数据说明

开发商户日志：data/dev_trading_log.csv（10000条数据）
测试验证商户日志：data/test_trading_log.csv（20000000条数据）

开发商户日志标准计算结果：data/dev_result.csv
测试验证商户日志标准计算结果：data/test_result.csv
