#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <stdarg.h>
#include <sys/stat.h>
#include <errno.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <unistd.h>
#ifdef _OPENMP
#include <omp.h>
#endif

#define MAX_LINE_LENGTH 1024
#define OPTIMIZED_MAX_MERCHANTS 50000
#define OPTIMIZED_MAX_RECORDS 20000000
#define WINDOW_15M (15 * 60)
#define WINDOW_2H (2 * 60 * 60)
#define WINDOW_6H (6 * 60 * 60)
#define DEFAULT_OUTPUT_DIR "."
#define MAX_PATH_LENGTH 512
#define HASH_SIZE 65536
#define THREAD_CACHE_SIZE 1024
#define CACHE_LINE_SIZE 64
#define PREFETCH_DISTANCE 8

// 交易记录结构
typedef struct {
    int mid;
    time_t trading_time;
    double amount;
    int seqno;
} TradeRecord;

// 统计结果结构
typedef struct {
    double sum;
    int frequency;
    double avg;
    double stddev;
    double zscore;
    double max;
    double min;
} WindowStats;

// 结果记录结构
typedef struct {
    int seqno;
    WindowStats stats_15m;
    WindowStats stats_2h;
    WindowStats stats_6h;
} ResultRecord;

// 商户数据结构
typedef struct {
    int mid;
    TradeRecord *records;
    int count;
    int capacity;
    int *sorted_indices;  // 按时间排序的索引
} MerchantData;

// 商户工作量结构（用于负载均衡）
typedef struct {
    int merchant_index;
    int workload;  // 记录数量
} MerchantWorkload;

// 哈希表节点
typedef struct HashNode {
    int mid;
    int merchant_index;
    struct HashNode *next;
} HashNode;

// 全局变量
TradeRecord *all_records = NULL;
int total_records = 0;
MerchantData *merchants = NULL;
int merchant_count = 0;
HashNode *hash_table[HASH_SIZE];
FILE *log_file = NULL;
ResultRecord *results = NULL;
int allocated_max_records = 0;  // 实际分配的最大记录数
int allocated_max_merchants = 0;  // 实际分配的最大商户数

// 线程本地缓存
static __thread TradeRecord thread_cache[THREAD_CACHE_SIZE];
static __thread int cache_pos = 0;

// 函数声明
int compare_by_time_and_seqno(const void *a, const void *b);
int compare_workload(const void *a, const void *b);
WindowStats calculate_window_stats_optimized(MerchantData *merchant, int target_seqno,
                                            time_t target_time, int window_seconds,
                                            double current_amount, int *last_start);

// 哈希函数
int hash_function(int mid) {
    return ((unsigned int)mid) % HASH_SIZE;
}

// 初始化哈希表
void init_hash_table() {
    for (int i = 0; i < HASH_SIZE; i++) {
        hash_table[i] = NULL;
    }
}

// 哈希表查找商户
MerchantData* find_merchant_fast(int mid) {
    int hash_index = hash_function(mid);
    HashNode *node = hash_table[hash_index];
    
    while (node) {
        if (node->mid == mid) {
            return &merchants[node->merchant_index];
        }
        node = node->next;
    }
    return NULL;
}

// 添加商户到哈希表
void add_merchant_to_hash(int mid, int merchant_index) {
    int hash_index = hash_function(mid);
    HashNode *new_node = malloc(sizeof(HashNode));
    new_node->mid = mid;
    new_node->merchant_index = merchant_index;
    new_node->next = hash_table[hash_index];
    hash_table[hash_index] = new_node;
}

// 创建目录
int create_directory(const char *path) {
    char tmp[MAX_PATH_LENGTH];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp), "%s", path);
    len = strlen(tmp);
    if (tmp[len - 1] == '/') {
        tmp[len - 1] = 0;
    }

    for (p = tmp + 1; *p; p++) {
        if (*p == '/') {
            *p = 0;
            if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
                if (errno != EEXIST) {
                    return -1;
                }
            }
            *p = '/';
        }
    }

    if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
        if (errno != EEXIST) {
            return -1;
        }
    }

    return 0;
}

// 初始化日志文件
int init_log_file(const char *output_dir) {
    char log_path[MAX_PATH_LENGTH];
    snprintf(log_path, sizeof(log_path), "%s/run.log", output_dir);
    log_file = fopen(log_path, "w");
    if (!log_file) {
        fprintf(stderr, "Cannot create log file: %s\n", log_path);
        return -1;
    }
    printf("Log file created: %s\n", log_path);
    return 0;
}

// 日志输出函数
void log_message(const char *format, ...) {
    va_list args;
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);

    printf("[%04d-%02d-%02d %02d:%02d:%02d] ",
           tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

    va_start(args, format);
    vprintf(format, args);
    va_end(args);

    if (log_file) {
        fprintf(log_file, "[%04d-%02d-%02d %02d:%02d:%02d] ",
                tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
        va_start(args, format);
        vfprintf(log_file, format, args);
        va_end(args);
        fflush(log_file);
    }
}

// 解析时间字符串
time_t parse_time(const char *time_str) {
    struct tm tm = {0};
    sscanf(time_str, "%d-%d-%d %d:%d:%d",
           &tm.tm_year, &tm.tm_mon, &tm.tm_mday,
           &tm.tm_hour, &tm.tm_min, &tm.tm_sec);
    tm.tm_year -= 1900;
    tm.tm_mon -= 1;
    return mktime(&tm);
}

// 简单粗暴的内存分配 - 直接分配足够大的内存
void allocate_memory_simple() {
    // 直接使用原来的大小，简单有效
    allocated_max_records = OPTIMIZED_MAX_RECORDS;
    allocated_max_merchants = OPTIMIZED_MAX_MERCHANTS;

    // 计算内存需求
    size_t records_size = sizeof(TradeRecord) * allocated_max_records;
    size_t merchants_size = sizeof(MerchantData) * allocated_max_merchants;
    size_t results_size = sizeof(ResultRecord) * allocated_max_records;

    log_message("Allocating memory: Records=%.1fMB, Merchants=%.1fMB, Results=%.1fMB\n",
               records_size/1024.0/1024.0, merchants_size/1024.0/1024.0,
               results_size/1024.0/1024.0);

    // 安全的内存分配
    all_records = malloc(records_size);
    merchants = malloc(merchants_size);
    results = malloc(results_size);

    if (!all_records || !merchants || !results) {
        log_message("ERROR: Memory allocation failed\n");
        exit(1);
    }

    // 初始化商户数据
    for (int i = 0; i < allocated_max_merchants; i++) {
        merchants[i].records = NULL;
        merchants[i].count = 0;
        merchants[i].capacity = 0;
        merchants[i].sorted_indices = NULL;
    }
}

// 查找或创建商户
MerchantData* find_or_create_merchant(int mid) {
    MerchantData *merchant = find_merchant_fast(mid);
    if (merchant) {
        return merchant;
    }
    
    if (merchant_count >= allocated_max_merchants) {
        fprintf(stderr, "Too many merchants (limit: %d)\n", allocated_max_merchants);
        exit(1);
    }
    
    merchant = &merchants[merchant_count];
    merchant->mid = mid;
    merchant->records = malloc(sizeof(TradeRecord) * 1000);
    merchant->count = 0;
    merchant->capacity = 1000;
    merchant->sorted_indices = NULL;
    
    add_merchant_to_hash(mid, merchant_count);
    merchant_count++;
    
    return merchant;
}

// 添加记录到商户
void add_record_to_merchant(MerchantData *merchant, TradeRecord *record) {
    if (merchant->count >= merchant->capacity) {
        merchant->capacity *= 2;
        merchant->records = realloc(merchant->records, 
                                  sizeof(TradeRecord) * merchant->capacity);
    }
    merchant->records[merchant->count++] = *record;
}

// 比较函数：先按时间，再按seqno
int compare_by_time_and_seqno(const void *a, const void *b) {
    const TradeRecord *ra = (const TradeRecord*)a;
    const TradeRecord *rb = (const TradeRecord*)b;
    
    if (ra->trading_time < rb->trading_time) return -1;
    if (ra->trading_time > rb->trading_time) return 1;
    
    return ra->seqno - rb->seqno;
}

// 商户工作量比较函数（降序排列）
int compare_workload(const void *a, const void *b) {
    const MerchantWorkload *wa = (const MerchantWorkload*)a;
    const MerchantWorkload *wb = (const MerchantWorkload*)b;
    return wb->workload - wa->workload;  // 降序排列
}

// 针对大数据量优化的窗口统计
WindowStats calculate_window_stats_optimized(MerchantData *merchant, int target_seqno,
                                            time_t target_time, int window_seconds,
                                            double current_amount, int *last_start) {
    WindowStats stats = {0};
    time_t window_start = target_time - window_seconds;

    // 简化的线性搜索，避免复杂的二分查找逻辑
    int start_pos = *last_start;

    // 确保start_pos在有效范围内
    if (start_pos >= merchant->count) {
        start_pos = merchant->count - 1;
    }
    if (start_pos < 0) {
        start_pos = 0;
    }

    // 向前搜索到窗口开始位置
    while (start_pos > 0 &&
           (merchant->records[start_pos].trading_time >= window_start &&
            merchant->records[start_pos].seqno <= target_seqno)) {
        start_pos--;
    }

    // 向后搜索到第一个有效记录
    while (start_pos < merchant->count &&
           (merchant->records[start_pos].trading_time < window_start ||
            merchant->records[start_pos].seqno > target_seqno)) {
        start_pos++;
    }

    *last_start = start_pos;

    // 快速统计计算
    double sum = 0.0, sum_sq = 0.0;
    double min_val = INFINITY, max_val = -INFINITY;
    int count = 0;

    for (int i = start_pos; i < merchant->count; i++) {
        TradeRecord *record = &merchant->records[i];

        if (record->seqno > target_seqno) break;
        if (record->trading_time > target_time) break;
        if (record->trading_time < window_start) continue;

        double amount = record->amount;
        sum += amount;
        sum_sq += amount * amount;
        count++;

        if (amount < min_val) min_val = amount;
        if (amount > max_val) max_val = amount;
    }

    // 计算统计值
    if (count > 0) {
        stats.sum = sum;
        stats.frequency = count;
        stats.avg = sum / count;
        stats.max = max_val;
        stats.min = min_val;

        if (count > 1) {
            double variance = (sum_sq - (sum * sum) / count) / (count - 1);
            stats.stddev = sqrt(fmax(0.0, variance));
            if (stats.stddev > 0) {
                stats.zscore = (current_amount - stats.avg) / stats.stddev;
            }
        }
    }

    return stats;
}

// 安全的优化窗口统计计算（回退到标准实现）
void calculate_all_windows_safe_optimized(MerchantData *merchant, int target_seqno,
                                         time_t target_time, double current_amount,
                                         WindowStats *stats_15m, WindowStats *stats_2h, WindowStats *stats_6h) {
    // 初始化统计结构
    memset(stats_15m, 0, sizeof(WindowStats));
    memset(stats_2h, 0, sizeof(WindowStats));
    memset(stats_6h, 0, sizeof(WindowStats));

    // 计算窗口起始时间
    time_t window_start_15m = target_time - WINDOW_15M;
    time_t window_start_2h = target_time - WINDOW_2H;
    time_t window_start_6h = target_time - WINDOW_6H;

    // 统计变量
    double sum_15m = 0, sum_2h = 0, sum_6h = 0;
    double sum_sq_15m = 0, sum_sq_2h = 0, sum_sq_6h = 0;
    double min_15m = INFINITY, max_15m = -INFINITY;
    double min_2h = INFINITY, max_2h = -INFINITY;
    double min_6h = INFINITY, max_6h = -INFINITY;
    int count_15m = 0, count_2h = 0, count_6h = 0;

    // 优化的循环展开处理
    int i = 0;

    // 每次处理4个记录以提高缓存效率
    for (; i + 3 < merchant->count; i += 4) {
        // 预取下一批数据
        if (i + 7 < merchant->count) {
            __builtin_prefetch(&merchant->records[i + 4], 0, 3);
            __builtin_prefetch(&merchant->records[i + 5], 0, 3);
            __builtin_prefetch(&merchant->records[i + 6], 0, 3);
            __builtin_prefetch(&merchant->records[i + 7], 0, 3);
        }

        // 处理4个记录
        for (int j = 0; j < 4; j++) {
            TradeRecord *record = &merchant->records[i + j];

            if (record->seqno > target_seqno) goto finish_processing;
            if (record->trading_time > target_time) goto finish_processing;

            double amount = record->amount;

            // 15分钟窗口
            if (record->trading_time >= window_start_15m) {
                sum_15m += amount;
                sum_sq_15m += amount * amount;
                count_15m++;
                if (amount < min_15m) min_15m = amount;
                if (amount > max_15m) max_15m = amount;
            }

            // 2小时窗口
            if (record->trading_time >= window_start_2h) {
                sum_2h += amount;
                sum_sq_2h += amount * amount;
                count_2h++;
                if (amount < min_2h) min_2h = amount;
                if (amount > max_2h) max_2h = amount;
            }

            // 6小时窗口
            if (record->trading_time >= window_start_6h) {
                sum_6h += amount;
                sum_sq_6h += amount * amount;
                count_6h++;
                if (amount < min_6h) min_6h = amount;
                if (amount > max_6h) max_6h = amount;
            }
        }
    }

    // 处理剩余的记录
    for (; i < merchant->count; i++) {
        TradeRecord *record = &merchant->records[i];

        if (record->seqno > target_seqno) break;
        if (record->trading_time > target_time) break;

        double amount = record->amount;

        // 15分钟窗口
        if (record->trading_time >= window_start_15m) {
            sum_15m += amount;
            sum_sq_15m += amount * amount;
            count_15m++;
            if (amount < min_15m) min_15m = amount;
            if (amount > max_15m) max_15m = amount;
        }

        // 2小时窗口
        if (record->trading_time >= window_start_2h) {
            sum_2h += amount;
            sum_sq_2h += amount * amount;
            count_2h++;
            if (amount < min_2h) min_2h = amount;
            if (amount > max_2h) max_2h = amount;
        }

        // 6小时窗口
        if (record->trading_time >= window_start_6h) {
            sum_6h += amount;
            sum_sq_6h += amount * amount;
            count_6h++;
            if (amount < min_6h) min_6h = amount;
            if (amount > max_6h) max_6h = amount;
        }
    }

finish_processing:

    // 计算15分钟窗口统计
    if (count_15m > 0) {
        stats_15m->sum = sum_15m;
        stats_15m->frequency = count_15m;
        stats_15m->avg = sum_15m / count_15m;
        stats_15m->max = max_15m;
        stats_15m->min = min_15m;
        if (count_15m > 1) {
            double variance = (sum_sq_15m - (sum_15m * sum_15m) / count_15m) / (count_15m - 1);
            stats_15m->stddev = sqrt(fmax(0.0, variance));
            if (stats_15m->stddev > 0) {
                stats_15m->zscore = (current_amount - stats_15m->avg) / stats_15m->stddev;
            }
        }
    }

    // 计算2小时窗口统计
    if (count_2h > 0) {
        stats_2h->sum = sum_2h;
        stats_2h->frequency = count_2h;
        stats_2h->avg = sum_2h / count_2h;
        stats_2h->max = max_2h;
        stats_2h->min = min_2h;
        if (count_2h > 1) {
            double variance = (sum_sq_2h - (sum_2h * sum_2h) / count_2h) / (count_2h - 1);
            stats_2h->stddev = sqrt(fmax(0.0, variance));
            if (stats_2h->stddev > 0) {
                stats_2h->zscore = (current_amount - stats_2h->avg) / stats_2h->stddev;
            }
        }
    }

    // 计算6小时窗口统计
    if (count_6h > 0) {
        stats_6h->sum = sum_6h;
        stats_6h->frequency = count_6h;
        stats_6h->avg = sum_6h / count_6h;
        stats_6h->max = max_6h;
        stats_6h->min = min_6h;
        if (count_6h > 1) {
            double variance = (sum_sq_6h - (sum_6h * sum_6h) / count_6h) / (count_6h - 1);
            stats_6h->stddev = sqrt(fmax(0.0, variance));
            if (stats_6h->stddev > 0) {
                stats_6h->zscore = (current_amount - stats_6h->avg) / stats_6h->stddev;
            }
        }
    }
}

// 安全的单个商户处理函数
void process_single_merchant_safe(MerchantData *merchant) {
    if (!merchant || !merchant->records || merchant->count <= 0) {
        return; // 安全检查
    }

    // 处理该商户的所有记录
    for (int i = 0; i < merchant->count; i++) {
        TradeRecord *record = &merchant->records[i];
        if (!record) continue; // 空指针检查

        int seqno = record->seqno;

        // 严格的边界检查
        if (seqno < 0 || seqno >= total_records) {
            log_message("WARNING: Invalid seqno %d for merchant %d, skipping\n",
                       seqno, merchant->mid);
            continue;
        }

        // 预取优化（安全版本）
        if (i + 1 < merchant->count) {
            __builtin_prefetch(&merchant->records[i + 1], 0, 3);
        }

        // 使用安全优化的窗口统计计算
        calculate_all_windows_safe_optimized(merchant, seqno, record->trading_time, record->amount,
                                            &results[seqno].stats_15m,
                                            &results[seqno].stats_2h,
                                            &results[seqno].stats_6h);
    }
}

// 内存映射优化的数据读取
int read_csv_mmap_optimized(const char *filename) {
    // 直接分配大内存，简单高效
    allocate_memory_simple();
    init_hash_table();

    // 使用内存映射读取文件
    int fd = open(filename, O_RDONLY);
    if (fd == -1) {
        fprintf(stderr, "Cannot open file: %s\n", filename);
        return -1;
    }

    struct stat file_stat;
    if (fstat(fd, &file_stat) == -1) {
        close(fd);
        fprintf(stderr, "Cannot get file size: %s\n", filename);
        return -1;
    }

    char *file_data = mmap(NULL, file_stat.st_size, PROT_READ, MAP_PRIVATE, fd, 0);
    if (file_data == MAP_FAILED) {
        close(fd);
        fprintf(stderr, "Cannot map file: %s\n", filename);
        return -1;
    }

    log_message("File mapped to memory, size: %.1f MB\n", file_stat.st_size / 1024.0 / 1024.0);

    // 快速解析CSV数据
    char *current = file_data;
    char *end = file_data + file_stat.st_size;

    // 跳过标题行
    while (current < end && *current != '\n') current++;
    if (current < end) current++; // 跳过换行符

    // 解析数据行
    while (current < end && total_records < allocated_max_records) {
        TradeRecord record;
        char *line_start = current;

        // 找到行结束
        char *line_end = current;
        while (line_end < end && *line_end != '\n') line_end++;

        if (line_end >= end) break;

        // 解析字段（避免strtok的开销）
        char *field_start = line_start;
        int field_count = 0;

        for (char *p = line_start; p <= line_end; p++) {
            if (p == line_end || *p == ',') {
                switch (field_count) {
                    case 0: // mid
                        record.mid = atoi(field_start);
                        break;
                    case 1: // trading_time
                        *p = '\0'; // 临时终止字符串
                        record.trading_time = parse_time(field_start);
                        *p = (p == line_end) ? '\n' : ','; // 恢复字符
                        break;
                    case 2: // amount
                        record.amount = atof(field_start);
                        break;
                    case 3: // seqno
                        record.seqno = atoi(field_start);
                        break;
                }
                field_count++;
                field_start = p + 1;
            }
        }

        if (field_count >= 4) {
            all_records[total_records++] = record;

            // 添加到商户数据
            MerchantData *merchant = find_or_create_merchant(record.mid);
            add_record_to_merchant(merchant, &record);
        }

        current = line_end + 1;

        // 进度报告
        if (total_records % 1000000 == 0) {
            log_message("Read %d million records...\n", total_records / 1000000);
        }
    }

    // 清理内存映射
    munmap(file_data, file_stat.st_size);
    close(fd);

    log_message("Read %d records from %s\n", total_records, filename);

    // 并行排序各商户数据
    log_message("Parallel sorting %d merchants...\n", merchant_count);

    #pragma omp parallel for schedule(dynamic, 10) num_threads(8)
    for (int i = 0; i < merchant_count; i++) {
        MerchantData *merchant = &merchants[i];
        qsort(merchant->records, merchant->count, sizeof(TradeRecord),
              compare_by_time_and_seqno);
    }

    return total_records;
}

// 标准安全的数据读取
int read_csv_standard(const char *filename) {
    // 直接分配大内存，简单高效
    allocate_memory_simple();
    init_hash_table();

    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Cannot open file: %s\n", filename);
        return -1;
    }

    char line[MAX_LINE_LENGTH];

    // 跳过标题行
    if (fgets(line, sizeof(line), file) == NULL) {
        fclose(file);
        return -1;
    }

    log_message("Starting to read CSV data...\n");

    while (fgets(line, sizeof(line), file) && total_records < allocated_max_records) {
        char *token;
        TradeRecord record;

        // 解析mid
        token = strtok(line, ",");
        if (!token) continue;
        record.mid = atoi(token);

        // 解析trading_time
        token = strtok(NULL, ",");
        if (!token) continue;
        record.trading_time = parse_time(token);

        // 解析amount
        token = strtok(NULL, ",");
        if (!token) continue;
        record.amount = atof(token);

        // 解析seqno
        token = strtok(NULL, ",\n");
        if (!token) continue;
        record.seqno = atoi(token);

        // 边界检查
        if (record.seqno < 0 || record.seqno >= allocated_max_records) {
            log_message("WARNING: Invalid seqno %d, skipping record\n", record.seqno);
            continue;
        }

        all_records[total_records++] = record;

        // 添加到商户数据
        MerchantData *merchant = find_or_create_merchant(record.mid);
        if (merchant) {
            add_record_to_merchant(merchant, &record);
        }

        // 进度报告
        if (total_records % 1000000 == 0) {
            log_message("Read %d million records...\n", total_records / 1000000);
        }
    }

    fclose(file);
    log_message("Read %d records from %s\n", total_records, filename);

    // 并行排序各商户数据
    log_message("Parallel sorting %d merchants...\n", merchant_count);

    #pragma omp parallel for schedule(dynamic, 10) num_threads(8)
    for (int i = 0; i < merchant_count; i++) {
        MerchantData *merchant = &merchants[i];
        if (merchant && merchant->records && merchant->count > 0) {
            qsort(merchant->records, merchant->count, sizeof(TradeRecord),
                  compare_by_time_and_seqno);
        }
    }

    return total_records;
}

// 负载均衡的并行处理
void process_with_load_balancing() {
    log_message("Processing with optimized 8-core parallelization...\n");

    // 预分配并初始化结果数组（保证seqno索引对应）
    #pragma omp parallel for schedule(static, 100000) num_threads(8)
    for (int i = 0; i < total_records; i++) {
        results[i].seqno = all_records[i].seqno;
        memset(&results[i].stats_15m, 0, sizeof(WindowStats));
        memset(&results[i].stats_2h, 0, sizeof(WindowStats));
        memset(&results[i].stats_6h, 0, sizeof(WindowStats));
    }

    // 创建工作量数组
    MerchantWorkload *workloads = malloc(sizeof(MerchantWorkload) * merchant_count);
    for (int i = 0; i < merchant_count; i++) {
        workloads[i].merchant_index = i;
        workloads[i].workload = merchants[i].count;
    }

    // 按工作量排序
    qsort(workloads, merchant_count, sizeof(MerchantWorkload), compare_workload);

    log_message("Starting parallel processing with load balancing...\n");

    // 动态调度处理大商户优先
    #pragma omp parallel for schedule(dynamic, 1) num_threads(8)
    for (int i = 0; i < merchant_count; i++) {
        int merchant_idx = workloads[i].merchant_index;
        MerchantData *merchant = &merchants[merchant_idx];

        // 处理该商户（使用安全版本）
        process_single_merchant_safe(merchant);

        // 进度报告（线程安全）
        if (i % 1000 == 0) {
            #pragma omp critical
            {
                log_message("Processed merchant %d/%d (%.1f%%) - workload: %d records\n",
                           i, merchant_count, (double)i / merchant_count * 100.0,
                           workloads[i].workload);
            }
        }
    }

    free(workloads);
    log_message("Parallel processing completed!\n");
}

// 优化的批量写入结果CSV
void write_results_optimized(const char *filename) {
    FILE *file = fopen(filename, "w");
    if (!file) {
        log_message("ERROR: Cannot create output file: %s\n", filename);
        return;
    }

    // 设置大缓冲区以减少系统调用
    const size_t buffer_size = 8 * 1024 * 1024; // 8MB缓冲区
    char *write_buffer = malloc(buffer_size);
    if (write_buffer) {
        setvbuf(file, write_buffer, _IOFBF, buffer_size);
    }

    // 写入标题行
    fprintf(file, "seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,");
    fprintf(file, "sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,");
    fprintf(file, "sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h\n");

    // 批量写入数据行
    const int batch_size = 10000;
    char line_buffer[512];

    for (int batch_start = 0; batch_start < total_records; batch_start += batch_size) {
        int batch_end = (batch_start + batch_size < total_records) ?
                        batch_start + batch_size : total_records;

        // 构建批量数据
        for (int i = batch_start; i < batch_end; i++) {
            ResultRecord *r = &results[i];

            // 使用snprintf构建单行，然后一次性写入
            int len = snprintf(line_buffer, sizeof(line_buffer),
                "%d,%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,"
                "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,"
                "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f\n",
                r->seqno, r->stats_15m.sum, r->stats_15m.frequency, r->stats_15m.avg,
                r->stats_15m.stddev, r->stats_15m.zscore, r->stats_15m.max, r->stats_15m.min,
                r->stats_2h.sum, r->stats_2h.frequency, r->stats_2h.avg,
                r->stats_2h.stddev, r->stats_2h.zscore, r->stats_2h.max, r->stats_2h.min,
                r->stats_6h.sum, r->stats_6h.frequency, r->stats_6h.avg,
                r->stats_6h.stddev, r->stats_6h.zscore, r->stats_6h.max, r->stats_6h.min);

            fwrite(line_buffer, 1, len, file);
        }

        // 每批次后刷新缓冲区
        if (batch_start % (batch_size * 100) == 0) {
            fflush(file);
            log_message("Written %d/%d records (%.1f%%)\n",
                       batch_end, total_records, (double)batch_end / total_records * 100.0);
        }
    }

    fclose(file);
    if (write_buffer) {
        free(write_buffer);
    }
    log_message("Results written to %s\n", filename);
}

// 并行写入结果CSV（分块写入）
void write_results_parallel(const char *filename) {
    log_message("Starting parallel write to %s\n", filename);

    const int num_chunks = 8; // 8个线程分别写入
    const int chunk_size = (total_records + num_chunks - 1) / num_chunks;

    // 创建临时文件名数组
    char temp_files[num_chunks][MAX_PATH_LENGTH];

    // 并行写入各个分块
    #pragma omp parallel for num_threads(num_chunks)
    for (int chunk = 0; chunk < num_chunks; chunk++) {
        int start_idx = chunk * chunk_size;
        int end_idx = (start_idx + chunk_size < total_records) ?
                      start_idx + chunk_size : total_records;

        if (start_idx >= total_records) continue;

        // 创建临时文件
        snprintf(temp_files[chunk], sizeof(temp_files[chunk]),
                "%s.tmp.%d", filename, chunk);

        FILE *temp_file = fopen(temp_files[chunk], "w");
        if (!temp_file) continue;

        // 设置大缓冲区
        const size_t buffer_size = 4 * 1024 * 1024; // 4MB
        char *write_buffer = malloc(buffer_size);
        if (write_buffer) {
            setvbuf(temp_file, write_buffer, _IOFBF, buffer_size);
        }

        // 写入数据块
        char line_buffer[512];
        for (int i = start_idx; i < end_idx; i++) {
            ResultRecord *r = &results[i];

            int len = snprintf(line_buffer, sizeof(line_buffer),
                "%d,%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,"
                "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,"
                "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f\n",
                r->seqno, r->stats_15m.sum, r->stats_15m.frequency, r->stats_15m.avg,
                r->stats_15m.stddev, r->stats_15m.zscore, r->stats_15m.max, r->stats_15m.min,
                r->stats_2h.sum, r->stats_2h.frequency, r->stats_2h.avg,
                r->stats_2h.stddev, r->stats_2h.zscore, r->stats_2h.max, r->stats_2h.min,
                r->stats_6h.sum, r->stats_6h.frequency, r->stats_6h.avg,
                r->stats_6h.stddev, r->stats_6h.zscore, r->stats_6h.max, r->stats_6h.min);

            fwrite(line_buffer, 1, len, temp_file);
        }

        fclose(temp_file);
        if (write_buffer) {
            free(write_buffer);
        }
    }

    // 合并临时文件
    FILE *final_file = fopen(filename, "w");
    if (!final_file) {
        log_message("ERROR: Cannot create final output file: %s\n", filename);
        return;
    }

    // 写入标题行
    fprintf(final_file, "seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,");
    fprintf(final_file, "sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,");
    fprintf(final_file, "sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h\n");

    // 合并各个临时文件
    char buffer[64 * 1024]; // 64KB缓冲区
    for (int chunk = 0; chunk < num_chunks; chunk++) {
        FILE *temp_file = fopen(temp_files[chunk], "r");
        if (temp_file) {
            size_t bytes_read;
            while ((bytes_read = fread(buffer, 1, sizeof(buffer), temp_file)) > 0) {
                fwrite(buffer, 1, bytes_read, final_file);
            }
            fclose(temp_file);
            unlink(temp_files[chunk]); // 删除临时文件
        }
    }

    fclose(final_file);
    log_message("Parallel write completed: %s\n", filename);
}

// 清理内存
void cleanup() {
    if (all_records) {
        free(all_records);
        all_records = NULL;
    }

    if (results) {
        free(results);
        results = NULL;
    }

    if (merchants) {
        for (int i = 0; i < merchant_count; i++) {
            if (merchants[i].records) {
                free(merchants[i].records);
            }
        }
        free(merchants);
        merchants = NULL;
    }

    // 清理哈希表
    for (int i = 0; i < HASH_SIZE; i++) {
        HashNode *node = hash_table[i];
        while (node) {
            HashNode *next = node->next;
            free(node);
            node = next;
        }
        hash_table[i] = NULL;
    }

    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <input_csv_file> [output_directory]\n", argv[0]);
        return 1;
    }

    const char *input_file = argv[1];
    const char *output_dir = (argc >= 3) ? argv[2] : DEFAULT_OUTPUT_DIR;

    // 设置OpenMP参数
    omp_set_num_threads(8);
    omp_set_dynamic(0);  // 禁用动态线程数调整

    printf("OpenMP enabled with %d threads\n", omp_get_max_threads());

    // 创建输出目录
    if (create_directory(output_dir) != 0) {
        fprintf(stderr, "Failed to create output directory: %s\n", output_dir);
        return 1;
    }

    // 初始化日志文件
    if (init_log_file(output_dir) != 0) {
        return 1;
    }

    // 生成输出文件路径
    char output_file[MAX_PATH_LENGTH];
    snprintf(output_file, sizeof(output_file), "%s/result.csv", output_dir);

    log_message("Starting optimized calculation for 8-core system...\n");
    log_message("Expected processing: 20M records, 50K merchants\n");
    log_message("Input file: %s\n", input_file);
    log_message("Output directory: %s\n", output_dir);

    clock_t start_time = clock();

    // 使用标准文件读取（更安全）
    if (read_csv_standard(input_file) < 0) {
        log_message("ERROR: Failed to read input file\n");
        cleanup();
        return 1;
    }

    log_message("Data loading completed. Starting parallel processing...\n");

    // 负载均衡的并行处理
    process_with_load_balancing();

    // 并行结果写入
    log_message("Writing results to file with parallel I/O...\n");
    write_results_parallel(output_file);

    // 计算执行时间
    clock_t end_time = clock();
    double execution_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;

    log_message("=== PERFORMANCE SUMMARY ===\n");
    log_message("Total execution time: %.4f seconds\n", execution_time);
    log_message("Processed %d records from %d merchants\n", total_records, merchant_count);
    log_message("Performance: %.0f records/second\n", total_records / execution_time);
    log_message("Average records per merchant: %.1f\n", (double)total_records / merchant_count);

    // 详细性能分析
    double throughput_mb_per_sec = (total_records * sizeof(TradeRecord)) / (execution_time * 1024.0 * 1024.0);
    log_message("Data throughput: %.1f MB/second\n", throughput_mb_per_sec);

    // 估算优化效果
    double theoretical_min_time = total_records / 1000000.0; // 假设每秒处理100万记录
    double efficiency = (theoretical_min_time / execution_time) * 100.0;
    log_message("Processing efficiency: %.1f%% of theoretical maximum\n", efficiency);

    // 内存使用统计
    size_t total_memory = sizeof(TradeRecord) * allocated_max_records +
                         sizeof(MerchantData) * allocated_max_merchants +
                         sizeof(ResultRecord) * allocated_max_records;
    log_message("Peak memory usage: %.1f MB\n", total_memory / 1024.0 / 1024.0);
    log_message("Memory bandwidth utilization: %.1f MB/s\n",
               (total_memory / 1024.0 / 1024.0) / execution_time);

    // 清理内存
    cleanup();

    log_message("Optimized parallel calculation completed successfully!\n");
    return 0;
}
