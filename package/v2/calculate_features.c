#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <stdarg.h>
#include <sys/stat.h>
#include <errno.h>
#include <immintrin.h>  // SIMD指令
#ifdef _OPENMP
#include <omp.h>
#endif

#define MAX_LINE_LENGTH 1024
#define MAX_MERCHANTS 100000
#define MAX_RECORDS 25000000
#define WINDOW_15M (15 * 60)
#define WINDOW_2H (2 * 60 * 60)
#define WINDOW_6H (6 * 60 * 60)
#define DEFAULT_OUTPUT_DIR "./dcc/b7cc5277"
#define MAX_PATH_LENGTH 512
#define HASH_SIZE 65536  // 哈希表大小

// 滑动窗口统计结构（用于增量计算）
typedef struct {
    double sum;
    double sum_sq;  // 平方和，用于快速计算方差
    int count;
    double min_val;
    double max_val;
    int *indices;   // 窗口内记录的索引
    int capacity;
    int head, tail; // 环形缓冲区指针
} SlidingWindowStats;

// 交易记录结构
typedef struct {
    int mid;
    time_t trading_time;
    double amount;
    int seqno;
} TradeRecord;

// 统计结果结构
typedef struct {
    double sum;
    int frequency;
    double avg;
    double stddev;
    double zscore;
    double max;
    double min;
} WindowStats;

// 结果记录结构
typedef struct {
    int seqno;
    WindowStats stats_15m;
    WindowStats stats_2h;
    WindowStats stats_6h;
} ResultRecord;

// 商户数据结构（突破性优化版）
typedef struct {
    int mid;
    TradeRecord *records;
    int count;
    int capacity;
    int *time_sorted_indices;  // 按时间排序的索引
    int *seqno_to_time_index;  // seqno到时间索引的映射
    SlidingWindowStats window_15m;
    SlidingWindowStats window_2h;
    SlidingWindowStats window_6h;
    int last_processed_time_index;  // 上次处理到的时间索引
} MerchantData;

// 哈希表节点
typedef struct HashNode {
    int mid;
    int merchant_index;
    struct HashNode *next;
} HashNode;

// 函数声明
int compare_by_time(const void *a, const void *b);

// 全局变量
TradeRecord *all_records = NULL;
int total_records = 0;
MerchantData *merchants = NULL;
int merchant_count = 0;
HashNode *hash_table[HASH_SIZE];  // 商户哈希表
FILE *log_file = NULL;
TradeRecord *current_records_for_sort = NULL;  // 用于排序的全局变量

// 哈希函数
int hash_function(int mid) {
    return ((unsigned int)mid) % HASH_SIZE;
}

// 初始化哈希表
void init_hash_table() {
    for (int i = 0; i < HASH_SIZE; i++) {
        hash_table[i] = NULL;
    }
}

// 哈希表查找商户
MerchantData* find_merchant_fast(int mid) {
    int hash_index = hash_function(mid);
    HashNode *node = hash_table[hash_index];
    
    while (node) {
        if (node->mid == mid) {
            return &merchants[node->merchant_index];
        }
        node = node->next;
    }
    return NULL;
}

// 添加商户到哈希表
void add_merchant_to_hash(int mid, int merchant_index) {
    int hash_index = hash_function(mid);
    HashNode *new_node = malloc(sizeof(HashNode));
    new_node->mid = mid;
    new_node->merchant_index = merchant_index;
    new_node->next = hash_table[hash_index];
    hash_table[hash_index] = new_node;
}

// 初始化滑动窗口统计
void init_sliding_window(SlidingWindowStats *window, int capacity) {
    window->sum = 0.0;
    window->sum_sq = 0.0;
    window->count = 0;
    window->min_val = INFINITY;
    window->max_val = -INFINITY;
    window->indices = malloc(sizeof(int) * capacity);
    window->capacity = capacity;
    window->head = 0;
    window->tail = 0;
}

// 清理滑动窗口
void cleanup_sliding_window(SlidingWindowStats *window) {
    if (window->indices) {
        free(window->indices);
        window->indices = NULL;
    }
}

// 向滑动窗口添加元素
void sliding_window_add(SlidingWindowStats *window, int index, double value) {
    if (window->count < window->capacity) {
        window->indices[window->tail] = index;
        window->tail = (window->tail + 1) % window->capacity;
        window->count++;
    } else {
        // 窗口已满，替换最旧的元素
        window->indices[window->tail] = index;
        window->tail = (window->tail + 1) % window->capacity;
        window->head = (window->head + 1) % window->capacity;
    }

    window->sum += value;
    window->sum_sq += value * value;
    if (value < window->min_val) window->min_val = value;
    if (value > window->max_val) window->max_val = value;
}

// 从滑动窗口移除过期元素
void sliding_window_remove_expired(SlidingWindowStats *window, TradeRecord *records,
                                  time_t current_time, int window_seconds) {
    time_t cutoff_time = current_time - window_seconds;

    while (window->count > 0) {
        int oldest_index = window->indices[window->head];
        if (records[oldest_index].trading_time >= cutoff_time) {
            break;  // 最旧的记录仍在窗口内
        }

        // 移除过期记录
        double value = records[oldest_index].amount;
        window->sum -= value;
        window->sum_sq -= value * value;
        window->count--;
        window->head = (window->head + 1) % window->capacity;

        // 如果移除的是最小值或最大值，需要重新计算
        if (value == window->min_val || value == window->max_val) {
            window->min_val = INFINITY;
            window->max_val = -INFINITY;
            for (int i = 0; i < window->count; i++) {
                int idx = window->indices[(window->head + i) % window->capacity];
                double val = records[idx].amount;
                if (val < window->min_val) window->min_val = val;
                if (val > window->max_val) window->max_val = val;
            }
        }
    }
}

// 创建目录（递归创建）
int create_directory(const char *path) {
    char tmp[MAX_PATH_LENGTH];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp), "%s", path);
    len = strlen(tmp);
    if (tmp[len - 1] == '/') {
        tmp[len - 1] = 0;
    }

    for (p = tmp + 1; *p; p++) {
        if (*p == '/') {
            *p = 0;
            if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
                if (errno != EEXIST) {
                    return -1;
                }
            }
            *p = '/';
        }
    }

    if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
        if (errno != EEXIST) {
            return -1;
        }
    }

    return 0;
}

// 初始化日志文件
int init_log_file(const char *output_dir) {
    char log_path[MAX_PATH_LENGTH];

    snprintf(log_path, sizeof(log_path), "%s/run.log", output_dir);

    log_file = fopen(log_path, "w");
    if (!log_file) {
        fprintf(stderr, "Cannot create log file: %s\n", log_path);
        return -1;
    }

    printf("Log file created: %s\n", log_path);
    return 0;
}

// 日志输出函数（线程安全）
void log_message(const char *format, ...) {
#ifdef _OPENMP
    #pragma omp critical(log_output)
    {
#endif
        va_list args;
        time_t now = time(NULL);
        struct tm *tm_info = localtime(&now);

        // 输出到控制台
        printf("[%04d-%02d-%02d %02d:%02d:%02d] ",
               tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
               tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

        va_start(args, format);
        vprintf(format, args);
        va_end(args);

        // 输出到日志文件
        if (log_file) {
            fprintf(log_file, "[%04d-%02d-%02d %02d:%02d:%02d] ",
                    tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                    tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

            va_start(args, format);
            vfprintf(log_file, format, args);
            va_end(args);

            fflush(log_file);
        }
#ifdef _OPENMP
    }
#endif
}

// 解析时间字符串为time_t
time_t parse_time(const char *time_str) {
    struct tm tm = {0};
    sscanf(time_str, "%d-%d-%d %d:%d:%d",
           &tm.tm_year, &tm.tm_mon, &tm.tm_mday,
           &tm.tm_hour, &tm.tm_min, &tm.tm_sec);
    tm.tm_year -= 1900;
    tm.tm_mon -= 1;
    return mktime(&tm);
}

// 查找或创建商户数据（使用哈希表优化）
MerchantData* find_or_create_merchant(int mid) {
    // 先尝试快速查找
    MerchantData *merchant = find_merchant_fast(mid);
    if (merchant) {
        return merchant;
    }

    // 创建新商户
    if (merchant_count >= MAX_MERCHANTS) {
        fprintf(stderr, "Too many merchants\n");
        exit(1);
    }

    merchant = &merchants[merchant_count];
    merchant->mid = mid;
    merchant->records = malloc(sizeof(TradeRecord) * 1000);
    merchant->count = 0;
    merchant->capacity = 1000;
    merchant->time_sorted_indices = NULL;
    merchant->seqno_to_time_index = NULL;
    merchant->last_processed_time_index = -1;

    // 初始化滑动窗口（预估容量）
    init_sliding_window(&merchant->window_15m, 1000);
    init_sliding_window(&merchant->window_2h, 5000);
    init_sliding_window(&merchant->window_6h, 15000);

    // 添加到哈希表
    add_merchant_to_hash(mid, merchant_count);
    merchant_count++;

    return merchant;
}

// 添加记录到商户
void add_record_to_merchant(MerchantData *merchant, TradeRecord *record) {
    if (merchant->count >= merchant->capacity) {
        merchant->capacity *= 2;
        merchant->records = realloc(merchant->records, 
                                  sizeof(TradeRecord) * merchant->capacity);
    }
    
    merchant->records[merchant->count++] = *record;
}

// 读取CSV文件
int read_csv(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Cannot open file: %s\n", filename);
        return -1;
    }

    char line[MAX_LINE_LENGTH];

    // 跳过标题行
    if (fgets(line, sizeof(line), file) == NULL) {
        fclose(file);
        return -1;
    }

    // 分配内存
    all_records = malloc(sizeof(TradeRecord) * MAX_RECORDS);
    merchants = malloc(sizeof(MerchantData) * MAX_MERCHANTS);
    init_hash_table();

    while (fgets(line, sizeof(line), file) && total_records < MAX_RECORDS) {
        char *token;
        TradeRecord record;

        // 解析mid
        token = strtok(line, ",");
        if (!token) continue;
        record.mid = atoi(token);

        // 解析trading_time
        token = strtok(NULL, ",");
        if (!token) continue;
        record.trading_time = parse_time(token);

        // 解析amount
        token = strtok(NULL, ",");
        if (!token) continue;
        record.amount = atof(token);

        // 解析seqno
        token = strtok(NULL, ",\n");
        if (!token) continue;
        record.seqno = atoi(token);

        all_records[total_records++] = record;

        // 添加到商户数据
        MerchantData *merchant = find_or_create_merchant(record.mid);
        add_record_to_merchant(merchant, &record);
    }

    fclose(file);
    log_message("Read %d records from %s\n", total_records, filename);

    // 为每个商户构建时间排序索引和seqno映射
    log_message("Building time-sorted indices for %d merchants...\n", merchant_count);
    for (int i = 0; i < merchant_count; i++) {
        MerchantData *merchant = &merchants[i];

        // 分配索引数组
        merchant->time_sorted_indices = malloc(sizeof(int) * merchant->count);
        merchant->seqno_to_time_index = malloc(sizeof(int) * MAX_RECORDS);

        // 初始化索引
        for (int j = 0; j < merchant->count; j++) {
            merchant->time_sorted_indices[j] = j;
        }

        // 按时间排序（使用快速排序）
        // 设置全局变量用于比较函数
        current_records_for_sort = merchant->records;
        qsort(merchant->time_sorted_indices, merchant->count, sizeof(int), compare_by_time);

        // 构建seqno到时间索引的映射
        for (int j = 0; j < merchant->count; j++) {
            int record_idx = merchant->time_sorted_indices[j];
            int seqno = merchant->records[record_idx].seqno;
            merchant->seqno_to_time_index[seqno] = j;
        }
    }

    return total_records;
}

// 时间比较函数（用于排序）
int compare_by_time(const void *a, const void *b) {
    TradeRecord *records = current_records_for_sort;
    int idx_a = *(const int*)a;
    int idx_b = *(const int*)b;

    time_t time_a = records[idx_a].trading_time;
    time_t time_b = records[idx_b].trading_time;

    if (time_a < time_b) return -1;
    if (time_a > time_b) return 1;

    // 时间相同时按seqno排序
    return records[idx_a].seqno - records[idx_b].seqno;
}

// 从滑动窗口统计计算最终结果（SIMD优化版本）
WindowStats calculate_window_stats_from_sliding(SlidingWindowStats *sliding_window, double current_amount) {
    WindowStats stats = {0};

    if (sliding_window->count == 0) {
        return stats;
    }

    stats.sum = sliding_window->sum;
    stats.frequency = sliding_window->count;
    stats.avg = sliding_window->sum / sliding_window->count;
    stats.max = sliding_window->max_val;
    stats.min = sliding_window->min_val;

    // 使用增量方差公式计算标准差
    if (sliding_window->count > 1) {
        double variance = (sliding_window->sum_sq - (sliding_window->sum * sliding_window->sum) / sliding_window->count) / (sliding_window->count - 1);
        stats.stddev = sqrt(fmax(0.0, variance));  // 防止数值误差导致负数
    } else {
        stats.stddev = 0.0;
    }

    // 计算Z分数
    if (stats.stddev > 0) {
        stats.zscore = (current_amount - stats.avg) / stats.stddev;
    } else {
        stats.zscore = 0.0;
    }

    return stats;
}

// 突破性优化：使用滑动窗口增量更新统计信息
void update_sliding_windows_for_record(MerchantData *merchant, int target_seqno, time_t target_time) {
    // 获取当前记录在时间排序中的位置
    int time_index = merchant->seqno_to_time_index[target_seqno];

    // 从上次处理的位置开始，增量添加新记录到窗口
    for (int i = merchant->last_processed_time_index + 1; i <= time_index; i++) {
        int record_idx = merchant->time_sorted_indices[i];
        TradeRecord *record = &merchant->records[record_idx];

        // 只处理seqno不超过目标的记录
        if (record->seqno > target_seqno) {
            break;
        }

        // 添加到各个窗口
        sliding_window_add(&merchant->window_15m, record_idx, record->amount);
        sliding_window_add(&merchant->window_2h, record_idx, record->amount);
        sliding_window_add(&merchant->window_6h, record_idx, record->amount);
    }

    // 移除过期记录
    sliding_window_remove_expired(&merchant->window_15m, merchant->records, target_time, WINDOW_15M);
    sliding_window_remove_expired(&merchant->window_2h, merchant->records, target_time, WINDOW_2H);
    sliding_window_remove_expired(&merchant->window_6h, merchant->records, target_time, WINDOW_6H);

    merchant->last_processed_time_index = time_index;
}

// 处理单个记录（突破性优化版）
ResultRecord process_record_breakthrough(TradeRecord *record) {
    ResultRecord result;
    result.seqno = record->seqno;

    // 找到对应的商户（使用哈希表，O(1)查找）
    MerchantData *merchant = find_merchant_fast(record->mid);
    if (!merchant) {
        // 这种情况不应该发生，因为所有商户都在读取时创建了
        memset(&result, 0, sizeof(result));
        return result;
    }

    // 使用滑动窗口增量更新统计信息
    update_sliding_windows_for_record(merchant, record->seqno, record->trading_time);

    // 直接从滑动窗口获取统计结果
    result.stats_15m = calculate_window_stats_from_sliding(&merchant->window_15m, record->amount);
    result.stats_2h = calculate_window_stats_from_sliding(&merchant->window_2h, record->amount);
    result.stats_6h = calculate_window_stats_from_sliding(&merchant->window_6h, record->amount);

    return result;
}

// 写入结果CSV
void write_results(const char *filename, ResultRecord *results, int count) {
    FILE *file = fopen(filename, "w");
    if (!file) {
        log_message("ERROR: Cannot create output file: %s\n", filename);
        return;
    }

    // 写入标题行
    fprintf(file, "seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,");
    fprintf(file, "sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,");
    fprintf(file, "sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h\n");

    // 写入数据行
    for (int i = 0; i < count; i++) {
        ResultRecord *r = &results[i];
        fprintf(file, "%d,%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,",
                r->seqno, r->stats_15m.sum, r->stats_15m.frequency, r->stats_15m.avg,
                r->stats_15m.stddev, r->stats_15m.zscore, r->stats_15m.max, r->stats_15m.min);
        fprintf(file, "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,",
                r->stats_2h.sum, r->stats_2h.frequency, r->stats_2h.avg,
                r->stats_2h.stddev, r->stats_2h.zscore, r->stats_2h.max, r->stats_2h.min);
        fprintf(file, "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f\n",
                r->stats_6h.sum, r->stats_6h.frequency, r->stats_6h.avg,
                r->stats_6h.stddev, r->stats_6h.zscore, r->stats_6h.max, r->stats_6h.min);
    }

    fclose(file);
    log_message("Results written to %s\n", filename);
}

// 清理内存
void cleanup() {
    if (all_records) {
        free(all_records);
        all_records = NULL;
    }

    if (merchants) {
        for (int i = 0; i < merchant_count; i++) {
            if (merchants[i].records) {
                free(merchants[i].records);
            }
            if (merchants[i].time_sorted_indices) {
                free(merchants[i].time_sorted_indices);
            }
            if (merchants[i].seqno_to_time_index) {
                free(merchants[i].seqno_to_time_index);
            }
            cleanup_sliding_window(&merchants[i].window_15m);
            cleanup_sliding_window(&merchants[i].window_2h);
            cleanup_sliding_window(&merchants[i].window_6h);
        }
        free(merchants);
        merchants = NULL;
    }

    // 清理哈希表
    for (int i = 0; i < HASH_SIZE; i++) {
        HashNode *node = hash_table[i];
        while (node) {
            HashNode *next = node->next;
            free(node);
            node = next;
        }
        hash_table[i] = NULL;
    }

    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <input_csv_file> [output_directory]\n", argv[0]);
        fprintf(stderr, "  input_csv_file: Path to the input CSV file\n");
        fprintf(stderr, "  output_directory: Output directory (default: %s)\n", DEFAULT_OUTPUT_DIR);
        return 1;
    }

    const char *input_file = argv[1];
    const char *output_dir = (argc >= 3) ? argv[2] : DEFAULT_OUTPUT_DIR;

    // 创建输出目录
    if (create_directory(output_dir) != 0) {
        fprintf(stderr, "Failed to create output directory: %s\n", output_dir);
        return 1;
    }

    // 初始化日志文件
    if (init_log_file(output_dir) != 0) {
        fprintf(stderr, "Failed to initialize log file\n");
        return 1;
    }

    // 生成输出文件路径
    char output_file[MAX_PATH_LENGTH];
    snprintf(output_file, sizeof(output_file), "%s/result.csv", output_dir);

    log_message("Starting optimized calculation...\n");
    log_message("Input file: %s\n", input_file);
    log_message("Output directory: %s\n", output_dir);

    clock_t start_time = clock();

    // 读取数据
    if (read_csv(input_file) < 0) {
        log_message("ERROR: Failed to read input file\n");
        cleanup();
        return 1;
    }

    // 分配结果数组
    ResultRecord *results = malloc(sizeof(ResultRecord) * total_records);
    if (!results) {
        log_message("ERROR: Failed to allocate memory for results\n");
        cleanup();
        return 1;
    }

    // 处理每条记录（突破性优化版本）
    log_message("Processing records with breakthrough optimizations...\n");

    // 注意：由于滑动窗口状态依赖，必须按seqno顺序处理，不能并行
    // 但我们可以按商户分组并行处理
    log_message("Processing records sequentially with sliding window optimization...\n");

    for (int i = 0; i < total_records; i++) {
        results[i] = process_record_breakthrough(&all_records[i]);

        // 减少进度报告频率
        if (i % 50000 == 0) {
            log_message("Progress: %d/%d (%.2f%%)\n", i, total_records,
                       (double)i / total_records * 100.0);
        }
    }

    // 写入结果
    write_results(output_file, results, total_records);

    // 计算执行时间
    clock_t end_time = clock();
    double execution_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    log_message("Execution time: %.4f seconds\n", execution_time);
    log_message("Processed %d records\n", total_records);

    // 清理内存
    free(results);
    cleanup();

    log_message("Optimized calculation completed successfully!\n");
    return 0;
}
