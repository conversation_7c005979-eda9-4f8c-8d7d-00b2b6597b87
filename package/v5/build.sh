#!/bin/bash
# 编译脚本 - calculate_features.c

echo "🚀 开始编译..."

# 检查源文件
if [ ! -f "calculate_features.c" ]; then
    echo "❌ 源文件不存在: calculate_features.c"
    exit 1
fi

# 编译命令 - 安全的优化选项
gcc -O3 -march=native -mtune=native -fopenmp \
    -funroll-loops -ffast-math \
    -DNDEBUG -fomit-frame-pointer \
    -finline-functions \
    -o calculate_features calculate_features.c -lm

if [ $? -eq 0 ]; then
    echo "✅ 编译成功: calculate_features"
    echo ""
    echo "🎯 运行示例:"
    echo "  export OMP_NUM_THREADS=4"
    echo "  ./calculate_features input.csv output_dir"
    echo ""
    echo "📊 支持数据量: 最大2500万记录"
    echo "🧠 内存需求: 约5GB"
    echo "⚡ 推荐线程数: 4"
else
    echo "❌ 编译失败"
    exit 1
fi
