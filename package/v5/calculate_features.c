#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <stdarg.h>
#include <sys/stat.h>
#include <errno.h>
#include <sys/wait.h>
#include <unistd.h>
#ifdef _OPENMP
#include <omp.h>
#endif

#define MAX_LINE_LENGTH 1024
#define MAX_MERCHANTS 100000
#define MAX_RECORDS 25000000
#define WINDOW_15M (15 * 60)
#define WINDOW_2H (2 * 60 * 60)
#define WINDOW_6H (6 * 60 * 60)
#define DEFAULT_OUTPUT_DIR "."
#define MAX_PATH_LENGTH 512
#define HASH_SIZE 65536

// 交易记录结构
typedef struct {
    int mid;
    time_t trading_time;
    double amount;
    int seqno;
} TradeRecord;

// 统计结果结构
typedef struct {
    double sum;
    int frequency;
    double avg;
    double stddev;
    double zscore;
    double max;
    double min;
} WindowStats;

// 结果记录结构
typedef struct {
    int seqno;
    WindowStats stats_15m;
    WindowStats stats_2h;
    WindowStats stats_6h;
} ResultRecord;

// 商户数据结构
typedef struct {
    int mid;
    TradeRecord *records;
    int count;
    int capacity;
    int *sorted_indices;  // 按时间排序的索引
    int last_15m_start, last_2h_start, last_6h_start;  // 缓存窗口起始位置
} MerchantData;

// 哈希表节点
typedef struct HashNode {
    int mid;
    int merchant_index;
    struct HashNode *next;
} HashNode;

// 全局变量
TradeRecord *all_records = NULL;
int total_records = 0;
MerchantData *merchants = NULL;
int merchant_count = 0;
HashNode *hash_table[HASH_SIZE];
FILE *log_file = NULL;
ResultRecord *results = NULL;

// 函数声明
int compare_by_time_and_seqno(const void *a, const void *b);

// 哈希函数
int hash_function(int mid) {
    return ((unsigned int)mid) % HASH_SIZE;
}

// 初始化哈希表
void init_hash_table() {
    for (int i = 0; i < HASH_SIZE; i++) {
        hash_table[i] = NULL;
    }
}

// 哈希表查找商户
MerchantData* find_merchant_fast(int mid) {
    int hash_index = hash_function(mid);
    HashNode *node = hash_table[hash_index];
    
    while (node) {
        if (node->mid == mid) {
            return &merchants[node->merchant_index];
        }
        node = node->next;
    }
    return NULL;
}

// 添加商户到哈希表
void add_merchant_to_hash(int mid, int merchant_index) {
    int hash_index = hash_function(mid);
    HashNode *new_node = malloc(sizeof(HashNode));
    new_node->mid = mid;
    new_node->merchant_index = merchant_index;
    new_node->next = hash_table[hash_index];
    hash_table[hash_index] = new_node;
}

// 创建目录
int create_directory(const char *path) {
    char tmp[MAX_PATH_LENGTH];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp), "%s", path);
    len = strlen(tmp);
    if (tmp[len - 1] == '/') {
        tmp[len - 1] = 0;
    }

    for (p = tmp + 1; *p; p++) {
        if (*p == '/') {
            *p = 0;
            if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
                if (errno != EEXIST) {
                    return -1;
                }
            }
            *p = '/';
        }
    }

    if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
        if (errno != EEXIST) {
            return -1;
        }
    }

    return 0;
}

// 初始化日志文件
int init_log_file(const char *output_dir) {
    char log_path[MAX_PATH_LENGTH];
    snprintf(log_path, sizeof(log_path), "%s/run.log", output_dir);
    log_file = fopen(log_path, "w");
    if (!log_file) {
        fprintf(stderr, "Cannot create log file: %s\n", log_path);
        return -1;
    }
    printf("Log file created: %s\n", log_path);
    return 0;
}

// 日志输出函数
void log_message(const char *format, ...) {
    va_list args;
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);

    printf("[%04d-%02d-%02d %02d:%02d:%02d] ",
           tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

    va_start(args, format);
    vprintf(format, args);
    va_end(args);

    if (log_file) {
        fprintf(log_file, "[%04d-%02d-%02d %02d:%02d:%02d] ",
                tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
        va_start(args, format);
        vfprintf(log_file, format, args);
        va_end(args);
        fflush(log_file);
    }
}

// 解析时间字符串
time_t parse_time(const char *time_str) {
    struct tm tm = {0};
    sscanf(time_str, "%d-%d-%d %d:%d:%d",
           &tm.tm_year, &tm.tm_mon, &tm.tm_mday,
           &tm.tm_hour, &tm.tm_min, &tm.tm_sec);
    tm.tm_year -= 1900;
    tm.tm_mon -= 1;
    return mktime(&tm);
}

// 查找或创建商户
MerchantData* find_or_create_merchant(int mid) {
    MerchantData *merchant = find_merchant_fast(mid);
    if (merchant) {
        return merchant;
    }
    
    if (merchant_count >= MAX_MERCHANTS) {
        fprintf(stderr, "Too many merchants\n");
        exit(1);
    }
    
    merchant = &merchants[merchant_count];
    merchant->mid = mid;
    merchant->records = malloc(sizeof(TradeRecord) * 1000);
    merchant->count = 0;
    merchant->capacity = 1000;
    merchant->sorted_indices = NULL;
    merchant->last_15m_start = 0;
    merchant->last_2h_start = 0;
    merchant->last_6h_start = 0;
    
    add_merchant_to_hash(mid, merchant_count);
    merchant_count++;

    return merchant;
}



// 添加记录到商户
void add_record_to_merchant(MerchantData *merchant, TradeRecord *record) {
    if (merchant->count >= merchant->capacity) {
        merchant->capacity *= 2;
        merchant->records = realloc(merchant->records, 
                                  sizeof(TradeRecord) * merchant->capacity);
    }
    merchant->records[merchant->count++] = *record;
}

// 比较函数：先按时间，再按seqno
int compare_by_time_and_seqno(const void *a, const void *b) {
    const TradeRecord *ra = (const TradeRecord*)a;
    const TradeRecord *rb = (const TradeRecord*)b;
    
    if (ra->trading_time < rb->trading_time) return -1;
    if (ra->trading_time > rb->trading_time) return 1;
    
    return ra->seqno - rb->seqno;
}

// 窗口统计计算（使用缓存的起始位置）
WindowStats calculate_window_stats(MerchantData *merchant, int target_seqno,
                                  time_t target_time, int window_seconds,
                                  double current_amount, int *last_start) {
    WindowStats stats = {0};
    
    time_t window_start = target_time - window_seconds;
    double sum = 0.0, sum_sq = 0.0;
    double min_val = INFINITY, max_val = -INFINITY;
    int count = 0;
    
    // 从缓存的起始位置开始搜索，利用时间局部性
    int start_pos = *last_start;
    
    // 向前调整起始位置（如果需要）
    while (start_pos > 0 && merchant->records[start_pos].trading_time >= window_start) {
        start_pos--;
    }
    
    // 向后调整起始位置（如果需要）
    while (start_pos < merchant->count && 
           (merchant->records[start_pos].trading_time < window_start || 
            merchant->records[start_pos].seqno > target_seqno)) {
        start_pos++;
    }
    
    // 更新缓存的起始位置
    *last_start = start_pos;
    
    // 从起始位置开始计算统计信息
    for (int i = start_pos; i < merchant->count; i++) {
        TradeRecord *record = &merchant->records[i];
        
        if (record->seqno > target_seqno) break;
        if (record->trading_time > target_time) break;
        if (record->trading_time < window_start) continue;
        
        double amount = record->amount;
        sum += amount;
        sum_sq += amount * amount;
        count++;
        
        if (amount < min_val) min_val = amount;
        if (amount > max_val) max_val = amount;
    }
    
    if (count == 0) {
        return stats;
    }
    
    stats.sum = sum;
    stats.frequency = count;
    stats.avg = sum / count;
    stats.max = max_val;
    stats.min = min_val;
    
    // 计算标准差
    if (count > 1) {
        double variance = (sum_sq - (sum * sum) / count) / (count - 1);
        stats.stddev = sqrt(fmax(0.0, variance));
    } else {
        stats.stddev = 0.0;
    }
    
    // 计算Z分数
    if (stats.stddev > 0) {
        stats.zscore = (current_amount - stats.avg) / stats.stddev;
    } else {
        stats.zscore = 0.0;
    }
    
    return stats;
}

// 读取CSV文件
int read_csv(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Cannot open file: %s\n", filename);
        return -1;
    }

    char line[MAX_LINE_LENGTH];

    // 跳过标题行
    if (fgets(line, sizeof(line), file) == NULL) {
        fclose(file);
        return -1;
    }

    // 分配内存
    all_records = malloc(sizeof(TradeRecord) * MAX_RECORDS);
    merchants = malloc(sizeof(MerchantData) * MAX_MERCHANTS);
    results = malloc(sizeof(ResultRecord) * MAX_RECORDS);
    init_hash_table();

    while (fgets(line, sizeof(line), file) && total_records < MAX_RECORDS) {
        char *token;
        TradeRecord record;

        // 解析mid
        token = strtok(line, ",");
        if (!token) continue;
        record.mid = atoi(token);

        // 解析trading_time
        token = strtok(NULL, ",");
        if (!token) continue;
        record.trading_time = parse_time(token);

        // 解析amount
        token = strtok(NULL, ",");
        if (!token) continue;
        record.amount = atof(token);

        // 解析seqno
        token = strtok(NULL, ",\n");
        if (!token) continue;
        record.seqno = atoi(token);

        all_records[total_records++] = record;

        // 添加到商户数据
        MerchantData *merchant = find_or_create_merchant(record.mid);
        add_record_to_merchant(merchant, &record);
    }

    fclose(file);
    log_message("Read %d records from %s\n", total_records, filename);

    // 为每个商户按时间排序记录
    log_message("Sorting records for %d merchants...\n", merchant_count);
    for (int i = 0; i < merchant_count; i++) {
        MerchantData *merchant = &merchants[i];
        qsort(merchant->records, merchant->count, sizeof(TradeRecord), compare_by_time_and_seqno);
    }

    return total_records;
}

// 批量处理
void process_all_records() {
    log_message("Processing records with cached window positions...\n");

    // 初始化结果数组
    for (int i = 0; i < total_records; i++) {
        results[i].seqno = all_records[i].seqno;
        memset(&results[i].stats_15m, 0, sizeof(WindowStats));
        memset(&results[i].stats_2h, 0, sizeof(WindowStats));
        memset(&results[i].stats_6h, 0, sizeof(WindowStats));
    }

    // 按商户分组处理，利用缓存的窗口位置
    for (int m = 0; m < merchant_count; m++) {
        MerchantData *merchant = &merchants[m];

        if (m % 1000 == 0) {
            log_message("Processing merchant %d/%d (%.2f%%)\n", m, merchant_count,
                       (double)m / merchant_count * 100.0);
        }

        // 重置缓存位置
        merchant->last_15m_start = 0;
        merchant->last_2h_start = 0;
        merchant->last_6h_start = 0;

        // 为该商户的所有记录计算窗口统计
        for (int i = 0; i < merchant->count; i++) {
            TradeRecord *record = &merchant->records[i];
            int seqno = record->seqno;

            // 计算三个时间窗口
            results[seqno].stats_15m = calculate_window_stats(
                merchant, seqno, record->trading_time, WINDOW_15M, record->amount, &merchant->last_15m_start);
            results[seqno].stats_2h = calculate_window_stats(
                merchant, seqno, record->trading_time, WINDOW_2H, record->amount, &merchant->last_2h_start);
            results[seqno].stats_6h = calculate_window_stats(
                merchant, seqno, record->trading_time, WINDOW_6H, record->amount, &merchant->last_6h_start);
        }
    }
}

// 子进程写入结果CSV
void write_results_child_process(const char *filename, int record_count) {
    FILE *file = fopen(filename, "w");
    if (!file) {
        fprintf(stderr, "ERROR: Cannot create output file: %s\n", filename);
        exit(1);
    }

    // 写入标题行
    fprintf(file, "seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,");
    fprintf(file, "sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,");
    fprintf(file, "sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h\n");

    // 写入数据行（按seqno顺序）
    for (int i = 0; i < record_count; i++) {
        ResultRecord *r = &results[i];
        fprintf(file, "%d,%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,",
                r->seqno, r->stats_15m.sum, r->stats_15m.frequency, r->stats_15m.avg,
                r->stats_15m.stddev, r->stats_15m.zscore, r->stats_15m.max, r->stats_15m.min);
        fprintf(file, "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,",
                r->stats_2h.sum, r->stats_2h.frequency, r->stats_2h.avg,
                r->stats_2h.stddev, r->stats_2h.zscore, r->stats_2h.max, r->stats_2h.min);
        fprintf(file, "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f\n",
                r->stats_6h.sum, r->stats_6h.frequency, r->stats_6h.avg,
                r->stats_6h.stddev, r->stats_6h.zscore, r->stats_6h.max, r->stats_6h.min);
    }

    fclose(file);
    printf("Results written to %s by child process\n", filename);
}

// 启动子进程写入结果
pid_t start_write_process(const char *filename, int record_count) {
    pid_t pid = fork();

    if (pid == -1) {
        log_message("ERROR: Failed to fork child process\n");
        return -1;
    }

    if (pid == 0) {
        // 子进程：写入结果文件
        write_results_child_process(filename, record_count);

        // 子进程清理内存并退出
        exit(0);
    }

    // 父进程：返回子进程PID
    return pid;
}

// 清理内存
void cleanup() {
    if (all_records) {
        free(all_records);
        all_records = NULL;
    }

    if (results) {
        free(results);
        results = NULL;
    }

    if (merchants) {
        for (int i = 0; i < merchant_count; i++) {
            if (merchants[i].records) {
                free(merchants[i].records);
            }
        }
        free(merchants);
        merchants = NULL;
    }

    // 清理哈希表
    for (int i = 0; i < HASH_SIZE; i++) {
        HashNode *node = hash_table[i];
        while (node) {
            HashNode *next = node->next;
            free(node);
            node = next;
        }
        hash_table[i] = NULL;
    }

    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <input_csv_file> [output_directory]\n", argv[0]);
        return 1;
    }

    const char *input_file = argv[1];
    const char *output_dir = (argc >= 3) ? argv[2] : DEFAULT_OUTPUT_DIR;

    // 创建输出目录
    if (create_directory(output_dir) != 0) {
        fprintf(stderr, "Failed to create output directory: %s\n", output_dir);
        return 1;
    }

    // 初始化日志文件
    if (init_log_file(output_dir) != 0) {
        return 1;
    }

    // 生成输出文件路径
    char output_file[MAX_PATH_LENGTH];
    snprintf(output_file, sizeof(output_file), "%s/result.csv", output_dir);

    log_message("Starting calculation with cached window positions...\n");
    log_message("Input file: %s\n", input_file);
    log_message("Output directory: %s\n", output_dir);

    clock_t start_time = clock();

    // 读取数据
    if (read_csv(input_file) < 0) {
        log_message("ERROR: Failed to read input file\n");
        cleanup();
        return 1;
    }

    // 处理所有记录
    process_all_records();

    // 计算主进程执行时间
    clock_t end_time = clock();
    double execution_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    log_message("Main process execution time: %.4f seconds\n", execution_time);
    log_message("Processed %d records\n", total_records);
    log_message("Performance: %.0f records/second\n", total_records / execution_time);

    // 启动子进程写入结果
    log_message("Starting child process to write results...\n");
    pid_t child_pid = start_write_process(output_file, total_records);

    if (child_pid == -1) {
        log_message("ERROR: Failed to start child process\n");
        cleanup();
        return 1;
    }

    log_message("Child process started (PID: %d), main process exiting...\n", child_pid);
    log_message("Main calculation completed successfully!\n");

    // 主进程清理内存（不包括results，子进程还需要使用）
    if (all_records) {
        free(all_records);
        all_records = NULL;
    }

    if (merchants) {
        for (int i = 0; i < merchant_count; i++) {
            if (merchants[i].records) {
                free(merchants[i].records);
            }
        }
        free(merchants);
        merchants = NULL;
    }

    // 清理哈希表
    for (int i = 0; i < HASH_SIZE; i++) {
        HashNode *node = hash_table[i];
        while (node) {
            HashNode *next = node->next;
            free(node);
            node = next;
        }
        hash_table[i] = NULL;
    }

    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }

    // 主进程结束，子进程继续运行写入文件
    // 注意：results数组不在这里释放，子进程会继承并使用它
    return 0;
}
