[2025-08-01 16:57:43] Starting calculation with cached window positions...
[2025-08-01 16:57:43] Input file: ../../data/test_trading_log.csv
[2025-08-01 16:57:43] Output directory: .
[2025-08-01 16:57:44] Read 222151 records from ../../data/test_trading_log.csv
[2025-08-01 16:57:44] Sorting records for 49452 merchants...
[2025-08-01 16:57:44] Processing records with cached window positions...
[2025-08-01 16:57:44] Processing merchant 0/49452 (0.00%)
[2025-08-01 16:57:44] Processing merchant 1000/49452 (2.02%)
[2025-08-01 16:57:44] Processing merchant 2000/49452 (4.04%)
[2025-08-01 16:57:44] Processing merchant 3000/49452 (6.07%)
[2025-08-01 16:57:44] Processing merchant 4000/49452 (8.09%)
[2025-08-01 16:57:44] Processing merchant 5000/49452 (10.11%)
[2025-08-01 16:57:44] Processing merchant 6000/49452 (12.13%)
[2025-08-01 16:57:44] Processing merchant 7000/49452 (14.16%)
[2025-08-01 16:57:44] Processing merchant 8000/49452 (16.18%)
[2025-08-01 16:57:44] Processing merchant 9000/49452 (18.20%)
[2025-08-01 16:57:44] Processing merchant 10000/49452 (20.22%)
[2025-08-01 16:57:44] Processing merchant 11000/49452 (22.24%)
[2025-08-01 16:57:44] Processing merchant 12000/49452 (24.27%)
[2025-08-01 16:57:44] Processing merchant 13000/49452 (26.29%)
[2025-08-01 16:57:44] Processing merchant 14000/49452 (28.31%)
[2025-08-01 16:57:44] Processing merchant 15000/49452 (30.33%)
[2025-08-01 16:57:44] Processing merchant 16000/49452 (32.35%)
[2025-08-01 16:57:44] Processing merchant 17000/49452 (34.38%)
[2025-08-01 16:57:44] Processing merchant 18000/49452 (36.40%)
[2025-08-01 16:57:44] Processing merchant 19000/49452 (38.42%)
[2025-08-01 16:57:44] Processing merchant 20000/49452 (40.44%)
[2025-08-01 16:57:44] Processing merchant 21000/49452 (42.47%)
[2025-08-01 16:57:44] Processing merchant 22000/49452 (44.49%)
[2025-08-01 16:57:44] Processing merchant 23000/49452 (46.51%)
[2025-08-01 16:57:44] Processing merchant 24000/49452 (48.53%)
[2025-08-01 16:57:44] Processing merchant 25000/49452 (50.55%)
[2025-08-01 16:57:44] Processing merchant 26000/49452 (52.58%)
[2025-08-01 16:57:44] Processing merchant 27000/49452 (54.60%)
[2025-08-01 16:57:44] Processing merchant 28000/49452 (56.62%)
[2025-08-01 16:57:44] Processing merchant 29000/49452 (58.64%)
[2025-08-01 16:57:44] Processing merchant 30000/49452 (60.66%)
[2025-08-01 16:57:44] Processing merchant 31000/49452 (62.69%)
[2025-08-01 16:57:44] Processing merchant 32000/49452 (64.71%)
[2025-08-01 16:57:44] Processing merchant 33000/49452 (66.73%)
[2025-08-01 16:57:44] Processing merchant 34000/49452 (68.75%)
[2025-08-01 16:57:44] Processing merchant 35000/49452 (70.78%)
[2025-08-01 16:57:44] Processing merchant 36000/49452 (72.80%)
[2025-08-01 16:57:44] Processing merchant 37000/49452 (74.82%)
[2025-08-01 16:57:44] Processing merchant 38000/49452 (76.84%)
[2025-08-01 16:57:44] Processing merchant 39000/49452 (78.86%)
[2025-08-01 16:57:44] Processing merchant 40000/49452 (80.89%)
[2025-08-01 16:57:44] Processing merchant 41000/49452 (82.91%)
[2025-08-01 16:57:44] Processing merchant 42000/49452 (84.93%)
[2025-08-01 16:57:44] Processing merchant 43000/49452 (86.95%)
[2025-08-01 16:57:44] Processing merchant 44000/49452 (88.98%)
[2025-08-01 16:57:44] Processing merchant 45000/49452 (91.00%)
[2025-08-01 16:57:44] Processing merchant 46000/49452 (93.02%)
[2025-08-01 16:57:44] Processing merchant 47000/49452 (95.04%)
[2025-08-01 16:57:44] Processing merchant 48000/49452 (97.06%)
[2025-08-01 16:57:44] Processing merchant 49000/49452 (99.09%)
[2025-08-01 16:57:44] Main process execution time: 0.2629 seconds
[2025-08-01 16:57:44] Processed 222151 records
[2025-08-01 16:57:44] Performance: 845124 records/second
[2025-08-01 16:57:44] Starting child process to write results...
[2025-08-01 16:57:44] Child process started (PID: 97841), main process exiting...
[2025-08-01 16:57:44] Main calculation completed successfully!
