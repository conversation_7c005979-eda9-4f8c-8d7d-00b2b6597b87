#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>

#define MAX_LINE_LENGTH 1024
#define MAX_MERCHANTS 100000
#define MAX_RECORDS 2000000
#define WINDOW_15M (15 * 60)
#define WINDOW_2H (2 * 60 * 60)
#define WINDOW_6H (6 * 60 * 60)

// 交易记录结构
typedef struct {
    int mid;
    time_t trading_time;
    double amount;
    int seqno;
} TradeRecord;

// 统计结果结构
typedef struct {
    double sum;
    int frequency;
    double avg;
    double stddev;
    double zscore;
    double max;
    double min;
} WindowStats;

// 结果记录结构
typedef struct {
    int seqno;
    WindowStats stats_15m;
    WindowStats stats_2h;
    WindowStats stats_6h;
} ResultRecord;

// 商户数据结构
typedef struct {
    int mid;
    TradeRecord *records;
    int count;
    int capacity;
} MerchantData;

// 全局变量
TradeRecord *all_records = NULL;
int total_records = 0;
MerchantData *merchants = NULL;
int merchant_count = 0;

// 解析时间字符串为time_t
time_t parse_time(const char *time_str) {
    struct tm tm = {0};
    sscanf(time_str, "%d-%d-%d %d:%d:%d", 
           &tm.tm_year, &tm.tm_mon, &tm.tm_mday,
           &tm.tm_hour, &tm.tm_min, &tm.tm_sec);
    tm.tm_year -= 1900;
    tm.tm_mon -= 1;
    return mktime(&tm);
}

// 查找或创建商户数据
MerchantData* find_or_create_merchant(int mid) {
    // 线性查找商户
    for (int i = 0; i < merchant_count; i++) {
        if (merchants[i].mid == mid) {
            return &merchants[i];
        }
    }
    
    // 创建新商户
    if (merchant_count >= MAX_MERCHANTS) {
        fprintf(stderr, "Too many merchants\n");
        exit(1);
    }
    
    MerchantData *merchant = &merchants[merchant_count++];
    merchant->mid = mid;
    merchant->records = malloc(sizeof(TradeRecord) * 1000);
    merchant->count = 0;
    merchant->capacity = 1000;
    
    return merchant;
}

// 添加记录到商户
void add_record_to_merchant(MerchantData *merchant, TradeRecord *record) {
    if (merchant->count >= merchant->capacity) {
        merchant->capacity *= 2;
        merchant->records = realloc(merchant->records, 
                                  sizeof(TradeRecord) * merchant->capacity);
    }
    
    merchant->records[merchant->count++] = *record;
}

// 读取CSV文件
int read_csv(const char *filename) {
    FILE *file = fopen(filename, "r");
    if (!file) {
        fprintf(stderr, "Cannot open file: %s\n", filename);
        return -1;
    }
    
    char line[MAX_LINE_LENGTH];
    int line_num = 0;
    
    // 跳过标题行
    if (fgets(line, sizeof(line), file) == NULL) {
        fclose(file);
        return -1;
    }
    
    // 分配内存
    all_records = malloc(sizeof(TradeRecord) * MAX_RECORDS);
    merchants = malloc(sizeof(MerchantData) * MAX_MERCHANTS);
    
    while (fgets(line, sizeof(line), file) && total_records < MAX_RECORDS) {
        char *token;
        TradeRecord record;
        
        // 解析mid
        token = strtok(line, ",");
        if (!token) continue;
        record.mid = atoi(token);
        
        // 解析trading_time
        token = strtok(NULL, ",");
        if (!token) continue;
        record.trading_time = parse_time(token);
        
        // 解析amount
        token = strtok(NULL, ",");
        if (!token) continue;
        record.amount = atof(token);
        
        // 解析seqno
        token = strtok(NULL, ",\n");
        if (!token) continue;
        record.seqno = atoi(token);
        
        all_records[total_records++] = record;
        
        // 添加到商户数据
        MerchantData *merchant = find_or_create_merchant(record.mid);
        add_record_to_merchant(merchant, &record);
        
        line_num++;
    }
    
    fclose(file);
    printf("Read %d records from %s\n", total_records, filename);
    return total_records;
}

// 计算窗口统计
WindowStats calculate_window_stats(double *amounts, int count, double current_amount) {
    WindowStats stats = {0};

    if (count == 0) {
        return stats;
    }

    // 基础统计
    stats.sum = 0.0;
    stats.max = amounts[0];
    stats.min = amounts[0];

    for (int i = 0; i < count; i++) {
        stats.sum += amounts[i];
        if (amounts[i] > stats.max) stats.max = amounts[i];
        if (amounts[i] < stats.min) stats.min = amounts[i];
    }

    stats.frequency = count;
    stats.avg = stats.sum / count;

    // 计算标准差
    if (count > 1) {
        double variance = 0.0;
        for (int i = 0; i < count; i++) {
            double diff = amounts[i] - stats.avg;
            variance += diff * diff;
        }
        variance /= (count - 1);  // 样本标准差
        stats.stddev = sqrt(variance);
    } else {
        stats.stddev = 0.0;
    }

    // 计算Z分数 (当前金额相对于窗口的Z分数)
    if (stats.stddev > 0) {
        stats.zscore = (current_amount - stats.avg) / stats.stddev;
    } else {
        stats.zscore = 0.0;
    }

    return stats;
}

// 获取窗口内的数据
int get_window_data(MerchantData *merchant, int target_seqno, time_t target_time,
                   int window_seconds, double *amounts) {
    int count = 0;
    time_t window_start = target_time - window_seconds;

    for (int i = 0; i < merchant->count; i++) {
        TradeRecord *record = &merchant->records[i];

        // 只考虑seqno <= target_seqno的记录
        if (record->seqno > target_seqno) {
            break;
        }

        // 检查是否在时间窗口内
        if (record->trading_time >= window_start && record->trading_time <= target_time) {
            amounts[count++] = record->amount;
        }
    }

    return count;
}

// 处理单个记录
ResultRecord process_record(TradeRecord *record) {
    ResultRecord result;
    result.seqno = record->seqno;

    // 找到对应的商户
    MerchantData *merchant = find_or_create_merchant(record->mid);

    // 为每个时间窗口计算统计
    double amounts[10000];  // 临时数组存储窗口内的金额

    // 15分钟窗口
    int count_15m = get_window_data(merchant, record->seqno, record->trading_time,
                                   WINDOW_15M, amounts);
    result.stats_15m = calculate_window_stats(amounts, count_15m, record->amount);

    // 2小时窗口
    int count_2h = get_window_data(merchant, record->seqno, record->trading_time,
                                  WINDOW_2H, amounts);
    result.stats_2h = calculate_window_stats(amounts, count_2h, record->amount);

    // 6小时窗口
    int count_6h = get_window_data(merchant, record->seqno, record->trading_time,
                                  WINDOW_6H, amounts);
    result.stats_6h = calculate_window_stats(amounts, count_6h, record->amount);

    return result;
}

// 写入结果CSV
void write_results(const char *filename, ResultRecord *results, int count) {
    FILE *file = fopen(filename, "w");
    if (!file) {
        fprintf(stderr, "Cannot create output file: %s\n", filename);
        return;
    }

    // 写入标题行
    fprintf(file, "seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,");
    fprintf(file, "sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,");
    fprintf(file, "sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h\n");

    // 写入数据行
    for (int i = 0; i < count; i++) {
        ResultRecord *r = &results[i];
        fprintf(file, "%d,%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,",
                r->seqno, r->stats_15m.sum, r->stats_15m.frequency, r->stats_15m.avg,
                r->stats_15m.stddev, r->stats_15m.zscore, r->stats_15m.max, r->stats_15m.min);
        fprintf(file, "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,",
                r->stats_2h.sum, r->stats_2h.frequency, r->stats_2h.avg,
                r->stats_2h.stddev, r->stats_2h.zscore, r->stats_2h.max, r->stats_2h.min);
        fprintf(file, "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f\n",
                r->stats_6h.sum, r->stats_6h.frequency, r->stats_6h.avg,
                r->stats_6h.stddev, r->stats_6h.zscore, r->stats_6h.max, r->stats_6h.min);
    }

    fclose(file);
    printf("Results written to %s\n", filename);
}

// 清理内存
void cleanup() {
    if (all_records) {
        free(all_records);
        all_records = NULL;
    }

    if (merchants) {
        for (int i = 0; i < merchant_count; i++) {
            if (merchants[i].records) {
                free(merchants[i].records);
            }
        }
        free(merchants);
        merchants = NULL;
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <input_csv_file>\n", argv[0]);
        return 1;
    }

    const char *input_file = argv[1];
    const char *output_file = "result.csv";

    printf("Starting calculation...\n");
    clock_t start_time = clock();

    // 读取数据
    if (read_csv(input_file) < 0) {
        fprintf(stderr, "Failed to read input file\n");
        cleanup();
        return 1;
    }

    // 分配结果数组
    ResultRecord *results = malloc(sizeof(ResultRecord) * total_records);
    if (!results) {
        fprintf(stderr, "Failed to allocate memory for results\n");
        cleanup();
        return 1;
    }

    // 处理每条记录
    printf("Processing records...\n");
    for (int i = 0; i < total_records; i++) {
        if (i % 1000 == 0) {
            printf("Progress: %d/%d\n", i, total_records);
        }
        results[i] = process_record(&all_records[i]);
    }

    // 写入结果
    write_results(output_file, results, total_records);

    // 计算执行时间
    clock_t end_time = clock();
    double execution_time = ((double)(end_time - start_time)) / CLOCKS_PER_SEC;
    printf("Execution time: %.4f seconds\n", execution_time);

    // 清理内存
    free(results);
    cleanup();

    printf("Calculation completed successfully!\n");
    return 0;
}
