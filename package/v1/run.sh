#!/bin/bash

# 商户销售数据时间窗口特征统计计算脚本
# 用法: ./run.sh <input_csv_file>

# 检查参数
if [ $# -ne 1 ]; then
    echo "Usage: $0 <input_csv_file>"
    exit 1
fi

INPUT_FILE="$1"

# 检查输入文件是否存在
if [ ! -f "$INPUT_FILE" ]; then
    echo "Error: Input file '$INPUT_FILE' does not exist"
    exit 1
fi

# 编译C程序
echo "Compiling C program..."
gcc -O3 -march=native -mtune=native -flto -funroll-loops -ffast-math \
    -o calculate_features calculate_features.c -lm

# 检查编译是否成功
if [ $? -ne 0 ]; then
    echo "Error: Compilation failed"
    exit 1
fi

echo "Compilation successful"

# 运行程序
echo "Running calculation..."
./calculate_features "$INPUT_FILE"

# 检查程序是否成功运行
if [ $? -ne 0 ]; then
    echo "Error: Program execution failed"
    exit 1
fi

echo "Calculation completed successfully"
