#!/usr/bin/env python3
"""
运行shell脚本并进行结果比对的Python脚本
运行sh脚本，传入输入文件路径，然后比对生成的result.csv与期望结果文件
"""

import subprocess
import time
import sys
import os
import argparse
import pandas as pd
import numpy as np
import logging
from typing import Dict, Any


def setup_logger(log_level: str = "INFO") -> logging.Logger:
    """
    设置日志记录器

    Args:
        log_level: 日志级别，默认为INFO

    Returns:
        配置好的logger对象
    """
    # 创建logger
    logger = logging.getLogger('run_and_benchmark')
    logger.setLevel(getattr(logging, log_level.upper()))

    # 如果已经有handler，先清除
    if logger.handlers:
        logger.handlers.clear()

    # 创建控制台handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))

    # 创建文件handler
    file_handler = logging.FileHandler('run_and_benchmark.log', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)

    # 创建formatter
    console_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 设置formatter
    console_handler.setFormatter(console_formatter)
    file_handler.setFormatter(file_formatter)

    # 添加handler到logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)

    return logger


def run_script_with_input(script_path: str, input_file: str, logger: logging.Logger) -> dict:
    """
    运行shell脚本并统计耗时

    Args:
        script_path: shell脚本路径
        input_file: 输入文件路径

    Returns:
        包含执行结果的字典
    """
    # 检查脚本是否存在
    if not os.path.exists(script_path):
        raise FileNotFoundError(f"脚本文件不存在: {script_path}")

    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        raise FileNotFoundError(f"输入文件不存在: {input_file}")

    # 确保脚本有执行权限
    os.chmod(script_path, 0o755)

    # 构建命令
    cmd = [script_path, input_file]
    logger.info(f"执行命令: {' '.join(cmd)}")

    # 记录开始时间
    start_time = time.perf_counter()

    try:
        # 运行脚本
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            check=False,
            cwd=os.getcwd()
        )

        # 记录结束时间
        end_time = time.perf_counter()
        execution_time = end_time - start_time

        # 构建结果字典
        execution_result = {
            'script_path': script_path,
            'input_file': input_file,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'execution_time': execution_time,
            'success': result.returncode == 0
        }

        logger.info(f"脚本执行完成，返回码: {result.returncode}")
        logger.info(f"执行时间: {execution_time:.4f}秒")

        return execution_result

    except Exception as e:
        logger.error(f"执行脚本时发生错误: {str(e)}")
        raise


def compare_csv_files(result_file: str, expected_file: str, logger: logging.Logger, tolerance: float = 1e-4) -> dict:
    """
    比对两个CSV文件，检查数值差异是否小于指定容差

    Args:
        result_file: 生成的结果文件路径
        expected_file: 期望的结果文件路径
        tolerance: 数值比较的容差，默认1e-4

    Returns:
        比对结果字典
    """
    comparison_result = {
        'files_exist': True,
        'comparison_success': False,
        'max_difference': 0.0,
        'different_cells': [],
        'error_message': None
    }

    try:
        # 检查文件是否存在
        if not os.path.exists(result_file):
            comparison_result['files_exist'] = False
            comparison_result['error_message'] = f"结果文件不存在: {result_file}"
            return comparison_result

        if not os.path.exists(expected_file):
            comparison_result['files_exist'] = False
            comparison_result['error_message'] = f"期望文件不存在: {expected_file}"
            return comparison_result

        # 读取CSV文件
        result_df = pd.read_csv(result_file)
        expected_df = pd.read_csv(expected_file)

        # 检查形状是否一致
        if result_df.shape != expected_df.shape:
            comparison_result['error_message'] = f"文件形状不一致: result {result_df.shape} vs expected {expected_df.shape}"
            return comparison_result

        # 检查列名是否一致
        if not result_df.columns.equals(expected_df.columns):
            comparison_result['error_message'] = f"列名不一致: result {list(result_df.columns)} vs expected {list(expected_df.columns)}"
            return comparison_result

        # 逐个比较数值列
        max_diff = 0.0
        different_cells = []

        for col in result_df.columns:
            if pd.api.types.is_numeric_dtype(result_df[col]) and pd.api.types.is_numeric_dtype(expected_df[col]):
                # 数值列比较
                diff = np.abs(result_df[col] - expected_df[col])
                max_col_diff = diff.max()

                if max_col_diff > max_diff:
                    max_diff = max_col_diff

                # 找出超过容差的单元格
                mask = diff > tolerance
                if mask.any():
                    indices = np.where(mask)[0]
                    for idx in indices:
                        different_cells.append({
                            'row': int(idx),
                            'column': col,
                            'result_value': float(result_df.iloc[idx][col]),
                            'expected_value': float(expected_df.iloc[idx][col]),
                            'difference': float(diff[idx])
                        })
            else:
                # 非数值列直接比较
                mask = result_df[col] != expected_df[col]
                if mask.any():
                    indices = np.where(mask)[0]
                    for idx in indices:
                        different_cells.append({
                            'row': int(idx),
                            'column': col,
                            'result_value': str(result_df.iloc[idx][col]),
                            'expected_value': str(expected_df.iloc[idx][col]),
                            'difference': 'string_mismatch'
                        })

        comparison_result['max_difference'] = max_diff
        comparison_result['different_cells'] = different_cells
        comparison_result['comparison_success'] = len(different_cells) == 0

        return comparison_result

    except Exception as e:
        comparison_result['error_message'] = f"比对过程中发生错误: {str(e)}"
        return comparison_result


def log_summary(execution_result: dict, comparison_result: dict, logger: logging.Logger):
    """记录执行摘要到日志"""
    logger.info("="*60)
    logger.info("脚本执行和比对摘要")
    logger.info("="*60)
    logger.info(f"脚本路径: {execution_result['script_path']}")
    logger.info(f"输入文件: {execution_result['input_file']}")
    logger.info(f"返回码: {execution_result['return_code']}")
    logger.info(f"执行状态: {'成功' if execution_result['success'] else '失败'}")
    logger.info(f"执行时间: {execution_result['execution_time']:.4f}秒")

    if comparison_result['files_exist']:
        if comparison_result['comparison_success']:
            logger.info("比对结果: ✓ 通过 (所有数值差异都小于1e-6)")
        else:
            logger.warning("比对结果: ✗ 失败")
            logger.warning(f"最大差异: {comparison_result['max_difference']:.2e}")
            logger.warning(f"不同单元格数量: {len(comparison_result['different_cells'])}")

            # 显示前几个不同的单元格
            if comparison_result['different_cells']:
                logger.warning("前几个不同的单元格:")
                for cell in comparison_result['different_cells'][:5]:
                    logger.warning(f"  行{cell['row']}, 列'{cell['column']}': "
                          f"结果={cell['result_value']}, 期望={cell['expected_value']}, "
                          f"差异={cell['difference']}")
                if len(comparison_result['different_cells']) > 5:
                    logger.warning(f"  ... 还有 {len(comparison_result['different_cells']) - 5} 个不同的单元格")
    else:
        logger.error(f"比对结果: ✗ 失败 - {comparison_result['error_message']}")

    if execution_result['stdout']:
        logger.info(f"标准输出:\n{execution_result['stdout']}")

    if execution_result['stderr']:
        logger.warning(f"标准错误:\n{execution_result['stderr']}")

    logger.info("="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="运行shell脚本并比对结果文件",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_and_benchmark.py script.sh input.txt expected_result.csv

说明:
  - script.sh: 要运行的shell脚本，脚本会接收一个输入文件参数
  - input.txt: 传递给脚本的输入文件路径
  - expected_result.csv: 用于比对的期望结果文件
  - 脚本运行后会在当前目录生成result.csv文件
  - 程序会比对result.csv和expected_result.csv，数值差异容差为1e-6
        """
    )

    parser.add_argument('script', help='要运行的shell脚本路径')
    parser.add_argument('input_file', help='传递给脚本的输入文件路径')
    parser.add_argument('expected_file', help='用于比对的期望结果文件路径')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别，默认为INFO')

    args = parser.parse_args()

    # 初始化logger
    logger = setup_logger(args.log_level)

    try:
        # 运行脚本
        logger.info("开始运行脚本...")
        execution_result = run_script_with_input(args.script, args.input_file, logger)

        if not execution_result['success']:
            logger.error(f"脚本执行失败，返回码: {execution_result['return_code']}")
            if execution_result['stderr']:
                logger.error(f"错误信息: {execution_result['stderr']}")
            sys.exit(1)

        # 比对结果文件
        logger.info("开始比对结果文件...")
        result_csv = "result.csv"
        comparison_result = compare_csv_files(result_csv, args.expected_file, logger)

        # 记录摘要
        log_summary(execution_result, comparison_result, logger)

        # 根据比对结果设置退出码
        if comparison_result['files_exist'] and comparison_result['comparison_success']:
            logger.info("✓ 所有检查都通过!")
            sys.exit(0)
        else:
            logger.error("✗ 检查失败!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
